
Sub WK5_MRP()
    On Error GoTo ErrorHandler

    Set WorkFileSummaryBook = ThisWorkbook
    Set WorkFileSummarySheet = WorkFileSummaryBook.Worksheets("Summary")
    WorkFileSummarySheet.Range("A1:CC800").Copy
    Set NewAddedWorkbook = Workbooks.Add

    ' 直接使用PasteSpecial粘贴值，避免重复粘贴操作
    ActiveSheet.Range("A1").PasteSpecial Paste:=xlPasteValues

    ' 清除剪贴板状态
    Application.CutCopyMode = False

    Columns("D:Q").Select
    Selection.Delete Shift:=xlToLeft
    Columns("A:B").Select
    Selection.Delete Shift:=xlToLeft
    Columns("F:BT").Select
    Selection.Delete Shift:=xlToLeft
    
    Rows("1:3").Delete
    MrpPlacePath = WorkFileSummaryBook.Worksheets("Settings").Range("A14").Value
    ChDir MrpPlacePath
    Application.DisplayAlerts = False
    
    NewAddedWorkbook.SaveAs fileName:=MrpPlacePath & "\MRP.xlsx", FileFormat:=xlOpenXMLWorkbook, CreateBackup:=False
    Application.DisplayAlerts = True
    NewAddedWorkbook.Close True
    MsgBox "MRP WK5 Done"
    Exit Sub

ErrorHandler:
    Application.CutCopyMode = False
    Application.DisplayAlerts = True
    MsgBox "发生错误: " & Err.Description & " (错误代码: " & Err.Number & ")", vbCritical

End Sub



